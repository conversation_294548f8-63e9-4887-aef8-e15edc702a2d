"use server";

import { COLLABORATOR_ENDPOINTS } from "@/modules/collaborator/api/endpoints";
import { ICollaboratorDto } from "@/modules/collaborator/types/find-all.dto";
import { createGetRequest } from "@/shared/lib/requests";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";

export interface IFindAllCollaboratorParams {
	page?: number;
	limit?: number;
	search?: string;
}

/**
 * Server Action para buscar todos os colaboradores
 * Garante execução no servidor e dados serializáveis
 */
export async function findAllCollaboratorAction(
	params: IFindAllCollaboratorParams = {}
): Promise<ApiResponse<IResponsePaginated<ICollaboratorDto>>> {
	const { page = 1, limit = 10, search = "" } = params;
	
	try {
		const response = await createGetRequest<IResponsePaginated<ICollaboratorDto>>(
			COLLABORATOR_ENDPOINTS.FIND_ALL({ page, limit, search })
		);
		
		return response;
	} catch (error) {
		return {
			success: false,
			data: {
				message: "Erro ao buscar colaboradores",
				details: error instanceof Error ? error.message : "Erro desconhecido",
			},
			status: 500,
		};
	}
}
