"use server";

import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { IFormDto } from "@/modules/inspection/types/forms/dtos/find-all.dto";
import { IFormFindByIdDto } from "@/modules/inspection/types/forms/dtos/find-by-id.dto";
import { createGetRequest } from "@/shared/lib/requests";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { IPaginationParameters } from "@/shared/types/pagination/types";

/**
 * Server Action para buscar todos os formulários
 * Garante execução no servidor e dados serializáveis
 */
export async function findAllFormsAction(
	params: IPaginationParameters = {}
): Promise<ApiResponse<IResponsePaginated<IFormDto>>> {
	const { page = 1, limit = 10, search = "" } = params;
	
	try {
		const response = await createGetRequest<IResponsePaginated<IFormDto>>(
			INSPECTION_FORM_ENDPOINTS.FIND_ALL({ page, limit, search })
		);
		
		return response;
	} catch (error) {
		return {
			success: false,
			data: {
				message: "Erro ao buscar formulários",
				details: error instanceof Error ? error.message : "Erro desconhecido",
			},
			status: 500,
		};
	}
}

/**
 * Server Action para buscar formulário por ID
 * Garante execução no servidor e dados serializáveis
 */
export async function findFormByIdAction(
	formId: string
): Promise<ApiResponse<IFormFindByIdDto>> {
	try {
		const response = await createGetRequest<IFormFindByIdDto>(
			INSPECTION_FORM_ENDPOINTS.FIND_BY_ID(formId)
		);
		
		return response;
	} catch (error) {
		return {
			success: false,
			data: {
				message: "Erro ao buscar formulário",
				details: error instanceof Error ? error.message : "Erro desconhecido",
			},
			status: 500,
		};
	}
}
