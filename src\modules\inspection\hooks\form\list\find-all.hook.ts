"use client";
import { findAllFormsAction } from "@/modules/inspection/actions/form.actions";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { useQuery } from "@tanstack/react-query";
import { IPaginationParameters } from "../../../../../shared/types/pagination/types";
import { inspectionKeys } from "../../../constants/query/keys";
import { IFormDto } from "../../../types/forms/dtos/find-all.dto";

export const useFindAllForms = ({ page = 1, limit = 10, search = "" }: IPaginationParameters = {}) => {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: inspectionKeys.forms.list({ page, limit, search }),
		queryFn: () => findAllFormsAction({ page, limit, search }),
		enabled: canRead(INSPECTION_SUBJECTS.INSPECTION_FORM),
	});

	const isNoDataFound = !data?.success && data?.status === 404;
	return {
		data: data?.success ? data.data.data : [],
		pagination: data?.success
			? {
					totalItems: data.data.totalItems,
					itemsPerPage: data.data.itemsPerPage,
					currentPage: data.data.currentPage,
					totalPages: data.data.totalPages,
				}
			: null,
		isLoading,
		hasError: isFetched && !data?.success && !isNoDataFound,
		error: !data?.success && !isNoDataFound && data?.data.message,
		isEmpty: isNoDataFound,
	};
};
