"use server";

import { CELL_ENDPOINTS } from "@/modules/cell/api/endpoints";
import { ICellDto } from "@/modules/cell/types/find-all.dto";
import { createGetRequest } from "@/shared/lib/requests";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { IPaginationParameters } from "@/shared/types/pagination/types";

/**
 * Server Action para buscar todas as células
 * Garante execução no servidor e dados serializáveis
 */
export async function findAllCellAction(
	params: IPaginationParameters = {}
): Promise<ApiResponse<IResponsePaginated<ICellDto>>> {
	const { page = 1, limit = 10, search = "" } = params;
	
	try {
		const response = await createGetRequest<IResponsePaginated<ICellDto>>(
			CELL_ENDPOINTS.FIND_ALL({ page, limit, search })
		);
		
		return response;
	} catch (error) {
		return {
			success: false,
			data: {
				message: "Erro ao buscar células",
				details: error instanceof Error ? error.message : "Erro desconhecido",
			},
			status: 500,
		};
	}
}
