"use server";

import { INSPECTION_FORMS_LINKS_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { IFormLinkDto } from "@/modules/inspection/types/form-link/dtos/find-all.dto";
import { createGetRequest } from "@/shared/lib/requests";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";

export interface IFormLinksParams {
	page?: number;
	limit?: number;
	search?: string;
}

/**
 * Server Action para buscar todos os links de formulários
 * Garante execução no servidor e dados serializáveis
 */
export async function findAllFormLinksAction(
	params: IFormLinksParams = {}
): Promise<ApiResponse<IResponsePaginated<IFormLinkDto>>> {
	const { page = 1, limit = 10, search = "" } = params;
	
	try {
		const response = await createGetRequest<IResponsePaginated<IFormLinkDto>>(
			INSPECTION_FORMS_LINKS_ENDPOINTS.FIND_ALL({ page, limit, search })
		);
		
		return response;
	} catch (error) {
		return {
			success: false,
			data: {
				message: "Erro ao buscar links de formulários",
				details: error instanceof Error ? error.message : "Erro desconhecido",
			},
			status: 500,
		};
	}
}
