import { useFindAllChatKnowledge } from "@/modules/chat/hooks/list/find-all.hook";
import { EmptyStateTable } from "@/shared/components/custom/empty/table-empty";
import { TableLoading } from "@/shared/components/custom/loading";
import { Pagination } from "@/shared/components/custom/pagination";
import { Skeleton } from "@/shared/components/shadcn/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { useIsMobile } from "@/shared/hooks/shadcn/use-mobile";
import { usePagination } from "@/shared/hooks/utils";
import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { ComponentIcon } from "lucide-react";
import { ChatCardMobile } from "./card-mobile";
import { chatAdminColumns } from "./columns";

interface TableChatAdminProps {
	searchTerm: string;
}

export const TableChatAdmin = ({ searchTerm }: TableChatAdminProps) => {
	const { currentPage, setCurrentPage, pageSize, setItemsPerPage } = usePagination();
	const isMobile = useIsMobile();

	const { data, isLoading, error, pagination, hasError } = useFindAllChatKnowledge({
		limit: pageSize,
		page: currentPage,
		search: searchTerm || "",
	});

	const table = useReactTable({
		data: data ?? [],
		columns: chatAdminColumns,
		getCoreRowModel: getCoreRowModel(),
		manualPagination: true,
		pageCount: pagination?.totalPages ?? 0,
	});

	if (isMobile) {
		return (
			<div className="space-y-4">
				{hasError ? (
					<div className="h-24 text-center text-red-500">Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}</div>
				) : isLoading ? (
					<Skeleton className="mx-auto h-6 w-48" />
				) : data && data.length ? (
					data.map(item => <ChatCardMobile key={item.id} item={item} />)
				) : (
					<EmptyStateTable
						searchTerm={searchTerm}
						icon={<ComponentIcon />}
						title="Nenhum conhecimento encontrado"
						description={searchTerm ? "Nenhum conhecimento corresponde ao termo pesquisado." : "Ainda não há conhecimentos cadastrados."}
						tip="Você pode tentar pesquisar por outros termos ou adicionar um novo conhecimento."
					/>
				)}
				{pagination && (
					<Pagination
						currentPage={pagination.currentPage}
						totalPages={pagination.totalPages}
						pageSize={pagination.itemsPerPage}
						totalItems={pagination.totalItems}
						onPageChange={setCurrentPage}
						onPageSizeChange={size => {
							setItemsPerPage(size);
							setCurrentPage(1);
						}}
						showPageSizeSelector
						showSelectedInfo
					/>
				)}
			</div>
		);
	}

	return (
		<div className="space-y-4">
			<div className="bg-background rounded-controls overflow-x-auto border">
				<Table className="table-fixed">
					<TableHeader className="bg-primary sticky top-0 z-10">
						{table.getHeaderGroups().map(headerGroup => (
							<TableRow key={headerGroup.id}>
								{headerGroup.headers.map(header => {
									const colDef = header.column?.columnDef as unknown as { size?: number } | undefined;
									const style = colDef?.size && header.colSpan === 1 ? { width: `${colDef.size}px` } : undefined;
									return (
										<TableHead
											key={header.id}
											colSpan={header.colSpan}
											style={style}
											className="font-semibold whitespace-nowrap text-white"
										>
											{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
										</TableHead>
									);
								})}
							</TableRow>
						))}
					</TableHeader>
					<TableBody>
						{hasError ? (
							<TableRow>
								<TableCell colSpan={chatAdminColumns.length} className="h-24 text-center text-red-500">
									Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}
								</TableCell>
							</TableRow>
						) : isLoading ? (
							<TableLoading columns={chatAdminColumns.length} rows={pageSize} />
						) : table.getRowModel().rows.length ? (
							table.getRowModel().rows.map(row => (
								<TableRow key={row.id} data-state={row.getIsSelected() && "selected"} className={`transition-colors`}>
									{row.getVisibleCells().map(cell => (
										<TableCell key={cell.id} className="px-4 text-center align-middle" title={String(cell.getValue() ?? "")}>
											{flexRender(cell.column.columnDef.cell, cell.getContext())}
										</TableCell>
									))}
								</TableRow>
							))
						) : (
							<TableRow>
								<TableCell colSpan={chatAdminColumns.length} className="h-24 text-center">
									<EmptyStateTable
										searchTerm={searchTerm}
										icon={<ComponentIcon />}
										title="Nenhum conhecimento encontrado"
										description={
											searchTerm ? "Nenhum conhecimento corresponde ao termo pesquisado." : "Ainda não há conhecimentos cadastrados."
										}
										tip="Você pode tentar pesquisar por outros termos ou adicionar um novo conhecimento."
									/>
								</TableCell>
							</TableRow>
						)}
					</TableBody>
				</Table>
			</div>
			{pagination && (
				<Pagination
					currentPage={pagination.currentPage}
					totalPages={pagination.totalPages}
					pageSize={pagination.itemsPerPage}
					totalItems={pagination.totalItems}
					onPageChange={setCurrentPage}
					onPageSizeChange={size => {
						setItemsPerPage(size);
						setCurrentPage(1);
					}}
					showPageSizeSelector
					showSelectedInfo
				/>
			)}
		</div>
	);
};
