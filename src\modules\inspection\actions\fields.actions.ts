"use server";

import { FIELDS_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { IFieldDto } from "@/modules/inspection/hooks/fields/list/find-all.hook";
import { createGetRequest } from "@/shared/lib/requests";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { IPaginationParameters } from "@/shared/types/pagination/types";

/**
 * Server Action para buscar todos os campos
 * Garante execução no servidor e dados serializáveis
 */
export async function findAllFieldsAction(
	params: IPaginationParameters = {}
): Promise<ApiResponse<IResponsePaginated<IFieldDto>>> {
	const { page = 1, limit = 10, search = "" } = params;
	
	try {
		const response = await createGetRequest<IResponsePaginated<IFieldDto>>(
			FIELDS_ENDPOINTS.FIND_ALL({ page, limit, search })
		);
		
		return response;
	} catch (error) {
		return {
			success: false,
			data: {
				message: "Erro ao buscar campos",
				details: error instanceof Error ? error.message : "Erro desconhecido",
			},
			status: 500,
		};
	}
}
