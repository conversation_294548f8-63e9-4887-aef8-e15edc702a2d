"use server";

import { CELL_COMPONENTS_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { ICellByComponentDto } from "@/modules/inspection/types/cell-components/dtos/find-all.dto";
import { createGetRequest } from "@/shared/lib/requests";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";

export interface ICellComponentsParams {
	page?: number;
	limit?: number;
	search?: string;
}

/**
 * Server Action para buscar todos os componentes de célula
 * Garante execução no servidor e dados serializáveis
 */
export async function findAllCellByComponentsAction(
	params: ICellComponentsParams = {}
): Promise<ApiResponse<IResponsePaginated<ICellByComponentDto>>> {
	const { page = 1, limit = 10, search = "" } = params;
	
	try {
		const response = await createGetRequest<IResponsePaginated<ICellByComponentDto>>(
			CELL_COMPONENTS_ENDPOINTS.FIND_ALL({ page, limit, search })
		);
		
		return response;
	} catch (error) {
		return {
			success: false,
			data: {
				message: "Erro ao buscar componentes de célula",
				details: error instanceof Error ? error.message : "Erro desconhecido",
			},
			status: 500,
		};
	}
}
