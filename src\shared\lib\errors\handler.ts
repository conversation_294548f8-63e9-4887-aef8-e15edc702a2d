import { IHandleResponseError } from "@/shared/types/requests/request.type";
import { AxiosError } from "axios";
import { ErrorExtractionService } from "./categorizer";

const errorExtractor = new ErrorExtractionService();

export function handleGlobalError(error: unknown): IHandleResponseError {
	const errorDetails = errorExtractor.extractErrorDetails(error);

	if (error instanceof AxiosError) {
		const status = error.response?.status ?? 0;
		// Garantir que apenas dados serializáveis sejam retornados
		const serializableDetails = error.response?.data ? JSON.parse(JSON.stringify(error.response.data)) : undefined;

		return {
			success: false,
			data: {
				message: errorDetails.message,
				method: error.config?.method,
				url: error.config?.url,
				details: serializableDetails,
				category: errorDetails.category,
				fields: errorDetails.fields,
				code: errorDetails.code,
				context: errorDetails.context ? JSON.parse(JSON.stringify(errorDetails.context)) : undefined,
			},
			status: status,
		};
	}

	if (error instanceof Error) {
		return {
			success: false,
			data: {
				message: errorDetails.message,
				method: undefined,
				url: undefined,
				details: errorDetails.originalError,
				category: errorDetails.category,
				code: errorDetails.code,
				context: errorDetails.context,
			},
			status: 500,
		};
	}

	return {
		success: false,
		data: {
			message: errorDetails.message,
			method: undefined,
			url: undefined,
			details: errorDetails.originalError,
			category: errorDetails.category,
			context: errorDetails.context,
		},
		status: 500,
	};
}
