import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { ProtectedComponent } from "@/shared/components/auth/protected";
import { But<PERSON> } from "@/shared/components/shadcn/button";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { Copy, Edit, Eye, Trash } from "lucide-react";
import { ConfirmCloneFormModal } from "../clone/confirm-modal";
import { ConfirmDeleteFormModal } from "../delete/confirm-modal";
import { ModalEditForm } from "../edit/modal";

export const FormListActions = ({ formId, title, hasLink }: { formId: string; title: string; hasLink?: boolean }) => {
	const cloneModal = useModal();
	const deleteModal = useModal();
	const editModal = useModal();

	return (
		<div className="pr-[10px] text-right">
			<ProtectedComponent action="create" subject={INSPECTION_SUBJECTS.INSPECTION_FORM}>
				<Button size="icon" onClick={cloneModal.openModal} className="group bg-primary/5 hover:bg-primary/40 h-8 w-8">
					<Copy className="text-primary h-4 w-4 group-hover:text-white" />
				</Button>
			</ProtectedComponent>
			<ProtectedComponent action={!hasLink ? "update" : "read"} subject={INSPECTION_SUBJECTS.INSPECTION_FORM}>
				<Button size="icon" onClick={editModal.openModal} className="bg-primary/5 hover:bg-primary/40 group ml-2 h-8 w-8">
					{!hasLink ? (
						<Edit className="text-primary h-4 w-4 group-hover:text-white" />
					) : (
						<Eye className="text-primary h-4 w-4 group-hover:text-white" />
					)}
				</Button>
			</ProtectedComponent>
			<ProtectedComponent action="delete" subject={INSPECTION_SUBJECTS.INSPECTION_FORM}>
				<Button size="icon" onClick={deleteModal.openModal} className="group ml-2 h-8 w-8 bg-red-500/5 hover:bg-red-500/30">
					<Trash className="h-4 w-4 text-red-500 group-hover:text-white" />
				</Button>
			</ProtectedComponent>
			<ConfirmCloneFormModal isOpen={cloneModal.isOpen} onClose={cloneModal.closeModal} title={title} formId={formId} />
			<ConfirmDeleteFormModal isOpen={deleteModal.isOpen} onClose={deleteModal.closeModal} title={title} formId={formId} />
			<ModalEditForm canEdit={!hasLink} isOpen={editModal.isOpen} onClose={editModal.closeModal} formId={formId} />
		</div>
	);
};
