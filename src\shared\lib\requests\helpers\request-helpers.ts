"use server";
import { ICreateRequest } from "@/shared/types/requests/create-request.type";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { createRequest } from "../index";

type HelperRequestParams<T> = Omit<ICreateRequest<T>, "method">;

export const createGetRequest = async <TSuccess>(path: string, options?: Omit<HelperRequestParams<never>, "path" | "body">): Promise<ApiResponse<TSuccess>> => {
	const response = await createRequest<TSuccess>({
		...options,
		path,
		method: "GET",
	});

	// Garantir que apenas dados serializáveis sejam retornados
	if (response.success) {
		return {
			success: true,
			data: JSON.parse(JSON.stringify(response.data)),
			status: response.status,
		};
	} else {
		return {
			success: false,
			data: JSON.parse(JSON.stringify(response.data)),
			status: response.status,
		};
	}
};

export const createPostRequest = async <TSuccess, TRequest = unknown>(
	path: string,
	body?: TRequest,
	options?: Omit<HelperRequestParams<TRequest>, "path" | "body">,
): Promise<ApiResponse<TSuccess>> => {
	const response = await createRequest<TSuccess, TRequest>({
		...options,
		path,
		method: "POST",
		body,
	});

	// Garantir que apenas dados serializáveis sejam retornados
	if (response.success) {
		return {
			success: true,
			data: JSON.parse(JSON.stringify(response.data)),
			status: response.status,
		};
	} else {
		return {
			success: false,
			data: JSON.parse(JSON.stringify(response.data)),
			status: response.status,
		};
	}
};

export const createPutRequest = async <TSuccess, TRequest = unknown>(
	path: string,
	body?: TRequest,
	options?: Omit<HelperRequestParams<TRequest>, "path" | "body">,
): Promise<ApiResponse<TSuccess>> => {
	const response = await createRequest<TSuccess, TRequest>({
		...options,
		path,
		method: "PUT",
		body,
	});

	// Garantir que apenas dados serializáveis sejam retornados
	if (response.success) {
		return {
			success: true,
			data: JSON.parse(JSON.stringify(response.data)),
			status: response.status,
		};
	} else {
		return {
			success: false,
			data: JSON.parse(JSON.stringify(response.data)),
			status: response.status,
		};
	}
};

export const createPatchRequest = async <TSuccess, TRequest = unknown>(
	path: string,
	body?: TRequest,
	options?: Omit<HelperRequestParams<TRequest>, "path" | "body">,
): Promise<ApiResponse<TSuccess>> => {
	const response = await createRequest<TSuccess, TRequest>({
		...options,
		path,
		method: "PATCH",
		body,
	});

	// Garantir que apenas dados serializáveis sejam retornados
	if (response.success) {
		return {
			success: true,
			data: JSON.parse(JSON.stringify(response.data)),
			status: response.status,
		};
	} else {
		return {
			success: false,
			data: JSON.parse(JSON.stringify(response.data)),
			status: response.status,
		};
	}
};

export const createDeleteRequest = async <TSuccess>(
	path: string,
	options?: Omit<HelperRequestParams<never>, "path" | "body">,
): Promise<ApiResponse<TSuccess>> => {
	const response = await createRequest<TSuccess>({
		...options,
		path,
		method: "DELETE",
	});

	// Garantir que apenas dados serializáveis sejam retornados
	if (response.success) {
		return {
			success: true,
			data: JSON.parse(JSON.stringify(response.data)),
			status: response.status,
		};
	} else {
		return {
			success: false,
			data: JSON.parse(JSON.stringify(response.data)),
			status: response.status,
		};
	}
};

export const createBatchRequestsRequest = async <T extends readonly unknown[]>(requests: { [K in keyof T]: () => Promise<T[K]> }): Promise<{
	[K in keyof T]: T[K] | Error;
}> => {
	const results = await Promise.allSettled(requests.map(request => request()));
	return results.map(result => (result.status === "fulfilled" ? result.value : result.reason)) as { [K in keyof T]: T[K] | Error };
};

export const createWithTimeoutRequest = async <TSuccess, TRequest = unknown>(
	params: ICreateRequest<TRequest>,
	timeoutMs: number,
): Promise<ApiResponse<TSuccess>> => {
	const response = await createRequest<TSuccess, TRequest>({
		...params,
		timeout: timeoutMs,
	});

	// Garantir que apenas dados serializáveis sejam retornados
	if (response.success) {
		return {
			success: true,
			data: JSON.parse(JSON.stringify(response.data)),
			status: response.status,
		};
	} else {
		return {
			success: false,
			data: JSON.parse(JSON.stringify(response.data)),
			status: response.status,
		};
	}
};

// const [users, posts, comments] = await batchRequests([
//     () => get<User[]>("/api/users"),
//     () => get<Post[]>("/api/posts"),
//     () => get<Comment[]>("/api/comments")
// ]);
