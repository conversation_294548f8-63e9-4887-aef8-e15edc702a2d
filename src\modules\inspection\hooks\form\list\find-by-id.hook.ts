import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { IFormFindByIdDto } from "@/modules/inspection/types/forms/dtos/find-by-id.dto";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { createGetRequest } from "@/shared/lib/requests";
import { useQuery } from "@tanstack/react-query";
import { inspectionKeys } from "../../../constants/query/keys";

export const useFormFindById = (formId: string, enabled: boolean) => {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: inspectionKeys.forms.detail(formId),
		queryFn: () => createGetRequest<IFormFindByIdDto>(INSPECTION_FORM_ENDPOINTS.FIND_BY_ID(formId)),
		enabled: canRead(INSPECTION_SUBJECTS.INSPECTION_FORM) && enabled,
	});

	const isNoDataFound = !data?.success && data?.status === 404;
	return {
		data: data?.success ? data.data : null,
		isLoading,
		hasError: !data?.success && isFetched && !isNoDataFound,
		error: !data?.success ? data?.data.message : undefined,
		isEmpty: isNoDataFound,
	};
};
