import { SUBJECTS } from "@/config/permissions";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { useQuery } from "@tanstack/react-query";
import { createGetRequest } from "../../../../../shared/lib/requests";
import { IResponsePaginated } from "../../../../../shared/types/requests/response-paginated.type";
import { CELL_COMPONENTS_ENDPOINTS } from "../../../api/endpoints";
import { inspectionKeys } from "../../../constants/query/keys";
import { ICellByComponentDto, ICellComponentsParamsDto } from "../../../types/cell-components/dtos/find-all.dto";

export const useFindAllCellByComponents = ({ page = 1, limit = 10, search = "" }: ICellComponentsParamsDto) => {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: inspectionKeys.cellByComponents.list({ page, limit, search }),
		queryFn: () => createGetRequest<IResponsePaginated<ICellByComponentDto>>(CELL_COMPONENTS_ENDPOINTS.FIND_ALL({ page, limit, search })),
		enabled: canRead(SUBJECTS.INSPECTION_CELL_PRODUCTION_BY_COMPONENT),
	});

	const isNoDataFound = !data?.success && data?.status === 404;

	return {
		data: data?.success ? data.data.data : [],
		pagination: data?.success
			? {
					totalItems: data.data.totalItems,
					itemsPerPage: data.data.itemsPerPage,
					currentPage: data.data.currentPage,
					totalPages: data.data.totalPages,
				}
			: null,
		isLoading,
		hasError: isFetched && !data?.success && !isNoDataFound,
		error: !data?.success && !isNoDataFound && data?.data.message,
		isEmpty: isNoDataFound,
	};
};
