"use server";

import { COLLAB_BY_SECTOR_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { ICollabBySectorParamsDto, IInspectionCollabBySector } from "@/modules/inspection/types/collaborator-by-sector/find-all.dto";
import { createGetRequest } from "@/shared/lib/requests";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";

/**
 * Server Action para buscar todos os colaboradores por setor
 * Garante execução no servidor e dados serializáveis
 */
export async function findAllCollabBySectorAction(
	params: ICollabBySectorParamsDto = {}
): Promise<ApiResponse<IResponsePaginated<IInspectionCollabBySector>>> {
	const { page = 1, limit = 10, search = "" } = params;
	
	try {
		const response = await createGetRequest<IResponsePaginated<IInspectionCollabBySector>>(
			COLLAB_BY_SECTOR_ENDPOINTS.FIND_ALL({ page, limit, search })
		);
		
		return response;
	} catch (error) {
		return {
			success: false,
			data: {
				message: "Erro ao buscar colaboradores por setor",
				details: error instanceof Error ? error.message : "Erro desconhecido",
			},
			status: 500,
		};
	}
}
