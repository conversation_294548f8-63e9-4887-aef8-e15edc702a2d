import { useCallback } from "react";

interface ChatInputState {
	value: string;
	onChange: (value: string) => void;
	onSend: (content: string) => Promise<void>;
	onStop?: () => void;
	disabled?: boolean;
	isStreaming?: boolean;
}

export const useChatInput = ({ value, onChange, onSend, onStop, disabled, isStreaming }: ChatInputState) => {
	const canSend = !disabled && !isStreaming && value.trim().length > 0;
	const canStop = isStreaming && !!onStop;

	const handleSend = useCallback(async () => {
		if (!canSend) return;
		const msg = value;
		onChange("");
		try {
			await onSend(msg);
		} catch (err) {
			console.error("Error sending message:", err);
		}
	}, [canSend, onSend, value, onChange]);

	const handleStop = useCallback(() => {
		if (canStop && onStop) {
			onStop();
		}
	}, [canStop, onStop]);

	const handleKeyDown = useCallback(
		(e: React.KeyboardEvent<HTMLTextAreaElement>) => {
			if (e.key === "Enter" && !e.shiftKey) {
				e.preventDefault();
				if (isStreaming) {
					handleStop();
				} else {
					handleSend();
				}
			}
		},
		[handleSend, handleStop, isStreaming],
	);

	return {
		canSend,
		canStop,
		handleKeyDown,
		handleSend,
		handleStop,
	};
};
