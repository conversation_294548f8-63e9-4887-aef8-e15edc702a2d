"use server";

import { MEASURES_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { IMeasuresDto } from "@/modules/inspection/hooks/measures/list/find-all.hook";
import { createGetRequest } from "@/shared/lib/requests";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";

export interface IFindAllMeasuresParams {
	page?: number;
	limit?: number;
	search?: string;
}

/**
 * Server Action para buscar todas as medidas
 * Garante execução no servidor e dados serializáveis
 */
export async function findAllMeasuresAction(
	params: IFindAllMeasuresParams = {}
): Promise<ApiResponse<IResponsePaginated<IMeasuresDto>>> {
	const { page = 1, limit = 10, search = "" } = params;
	
	try {
		const response = await createGetRequest<IResponsePaginated<IMeasuresDto>>(
			MEASURES_ENDPOINTS.FIND_ALL({ page, limit, search })
		);
		
		return response;
	} catch (error) {
		return {
			success: false,
			data: {
				message: "Erro ao buscar medidas",
				details: error instanceof Error ? error.message : "Erro desconhecido",
			},
			status: 500,
		};
	}
}
