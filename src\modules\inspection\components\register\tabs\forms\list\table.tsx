import { useFindAllForms } from "@/modules/inspection/hooks/form/list/find-all.hook";
import { EmptyStateTable } from "@/shared/components/custom/empty/table-empty";
import { TableLoading } from "@/shared/components/custom/loading";
import { Pagination } from "@/shared/components/custom/pagination/pagination";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { useIsMobile } from "@/shared/hooks/shadcn/use-mobile";
import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { FormInputIcon } from "lucide-react";
import React from "react";
import { usePagination } from "../../../../../../../shared/hooks/utils";
import { FormCardMobile } from "./card-mobile";
import { inspectionFormColumns } from "./columns";

interface IFormulariosTabProps {
	searchTerm?: string;
}

export const FormTable: React.FC<IFormulariosTabProps> = ({ searchTerm }) => {
	// const [rowSelection, setRowSelection] = React.useState({});
	const { currentPage, setCurrentPage, pageSize, setItemsPerPage } = usePagination();
	const isMobile = useIsMobile();

	const { data, pagination, isLoading, hasError, error } = useFindAllForms({
		page: currentPage,
		limit: pageSize,
		search: searchTerm || "",
	});

	const table = useReactTable({
		data: data ?? [],
		columns: inspectionFormColumns,
		enableRowSelection: true,
		getCoreRowModel: getCoreRowModel(),
		manualPagination: true,
		pageCount: pagination?.totalPages ?? 0,
	});

	const paginationProps = {
		currentPage: pagination?.currentPage ?? currentPage,
		totalPages: pagination?.totalPages ?? 0,
		pageSize: pagination?.itemsPerPage ?? pageSize,
		totalItems: pagination?.totalItems ?? 0,
		onPageChange: setCurrentPage,
		onPageSizeChange: (size: number) => {
			setItemsPerPage(size);
			setCurrentPage(1);
		},
		showPageSizeSelector: true,
		showSelectedInfo: true,
	};

	const Empty = () => (
		<EmptyStateTable
			searchTerm={searchTerm}
			icon={<FormInputIcon />}
			title="Nenhum formulário encontrado"
			description={searchTerm ? "Nenhum formulário corresponde ao termo pesquisado." : "Ainda não há formulários cadastrados."}
			tip="Você pode tentar pesquisar por outros termos ou cadastrar um novo formulário."
		/>
	);

	const renderMobile = () => {
		if (hasError) return <div className="text-center text-red-500">Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}</div>;
		if (isLoading) return <TableLoading columns={inspectionFormColumns.length} />;
		if (!data || !data.length) return <Empty />;

		return (
			<>
				<div className="flex flex-col gap-4">
					{data.map((form, idx) => (
						<FormCardMobile key={form.id} form={form} index={idx} />
					))}
				</div>
				{pagination && <Pagination {...paginationProps} />}
			</>
		);
	};

	const renderDesktop = () => {
		const rows = table.getRowModel().rows;
		return (
			<>
				<div className="bg-background rounded-controls overflow-x-auto border">
					<Table className="table-fixed">
						<TableHeader className="bg-primary sticky top-0 z-10">
							{table.getHeaderGroups().map(headerGroup => (
								<TableRow key={headerGroup.id}>
									{headerGroup.headers.map(header => {
										const colDef = header.column?.columnDef as unknown as { size?: number } | undefined;
										const style = colDef?.size && header.colSpan === 1 ? { width: `${colDef.size}px` } : undefined;
										return (
											<TableHead
												key={header.id}
												colSpan={header.colSpan}
												style={style}
												className="font-semibold whitespace-nowrap text-white"
											>
												{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
											</TableHead>
										);
									})}
								</TableRow>
							))}
						</TableHeader>

						<TableBody>
							{hasError ? (
								<TableRow>
									<TableCell colSpan={inspectionFormColumns.length} className="h-24 text-center text-red-500">
										Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}
									</TableCell>
								</TableRow>
							) : isLoading ? (
								<TableLoading columns={inspectionFormColumns.length} rows={pageSize} />
							) : rows.length ? (
								rows.map(row => (
									<TableRow key={row.id} data-state={row.getIsSelected() && "selected"} className="transition-colors">
										{row.getVisibleCells().map(cell => (
											<TableCell
												key={cell.id}
												className="max-w-[260px] overflow-hidden text-ellipsis whitespace-nowrap"
												title={String(cell.getValue() ?? "")}
											>
												{flexRender(cell.column.columnDef.cell, cell.getContext())}
											</TableCell>
										))}
									</TableRow>
								))
							) : (
								<TableRow>
									<TableCell colSpan={inspectionFormColumns.length} className="h-24 text-center">
										<Empty />
									</TableCell>
								</TableRow>
							)}
						</TableBody>
					</Table>
				</div>

				{pagination && <Pagination {...paginationProps} />}
			</>
		);
	};

	return <div className="space-y-4">{isMobile ? renderMobile() : renderDesktop()}</div>;
};
