"use client";
import { findAllMeasuresAction } from "@/modules/inspection/actions/measures.actions";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { useQuery } from "@tanstack/react-query";
import { inspectionKeys } from "../../../constants/query/keys";

export interface IMeasuresDto {
	id: string;
	name: string;
	abbreviation: string;
}

export interface IFindAllMeasureaParamns {
	page?: number;
	limit?: number;
	search?: string;
}

export default function useFindAllMeasures({ page = 1, limit = 10, search = "" }: IFindAllMeasureaParamns) {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: inspectionKeys.measures.list({ page, limit, search }),
		queryFn: () => findAllMeasuresAction({ page, limit, search }),
		enabled: canRead(INSPECTION_SUBJECTS.INSPECTION_MEASURE),
	});

	const isNoDataFound = !data?.success && data?.status === 404;

	return {
		data: data?.success ? data.data.data : [],
		pagination: data?.success
			? {
					totalItems: data.data.totalItems,
					itemsPerPage: data.data.itemsPerPage,
					currentPage: data.data.currentPage,
					totalPages: data.data.totalPages,
				}
			: null,
		isLoading,
		hasError: isFetched && !data?.success && !isNoDataFound,
		error: !data?.success && !isNoDataFound && data?.data.message,
		isEmpty: isNoDataFound,
	};
}
