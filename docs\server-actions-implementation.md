# Implementação de Server Actions para Requisições HTTP

## Problema Identificado

O erro `Only plain objects can be passed to Client Components from Server Components. AxiosHeaders objects are not supported.` ocorria porque:

1. Os helpers de request (`createGetRequest`, `createPostRequest`, etc.) tinham a diretiva `"use server"` mas eram chamados diretamente dentro de hooks que executavam no cliente
2. O React Query executava essas funções no cliente, tentando serializar objetos Axios não serializáveis
3. Objetos `AxiosHeaders` e outras propriedades internas do Axios não podem ser passados entre Server e Client Components

## Solução Implementada

### 1. Garantia de Serialização nos Helpers de Request

Modificamos todos os helpers em `src/shared/lib/requests/helpers/request-helpers.ts` para garantir que apenas dados serializáveis sejam retornados:

```typescript
export const createGetRequest = async <TSuccess>(
  path: string, 
  options?: Omit<HelperRequestParams<never>, "path" | "body">
): Promise<ApiResponse<TSuccess>> => {
  const response = await createRequest<TSuccess>({
    ...options,
    path,
    method: "GET",
  });
  
  // Garantir que apenas dados serializáveis sejam retornados
  if (response.success) {
    return {
      success: true,
      data: JSON.parse(JSON.stringify(response.data)),
      status: response.status,
    };
  } else {
    return {
      success: false,
      data: JSON.parse(JSON.stringify(response.data)),
      status: response.status,
    };
  }
};
```

### 2. Criação de Server Actions Específicas

Criamos Server Actions dedicadas para cada módulo que garantem execução no servidor:

#### Formulários (`src/modules/inspection/actions/form.actions.ts`)
- `findAllFormsAction()` - Busca todos os formulários
- `findFormByIdAction()` - Busca formulário por ID

#### Medidas (`src/modules/inspection/actions/measures.actions.ts`)
- `findAllMeasuresAction()` - Busca todas as medidas

#### Componentes de Célula (`src/modules/inspection/actions/cell-components.actions.ts`)
- `findAllCellByComponentsAction()` - Busca componentes de célula

#### Colaboradores (`src/modules/collaborator/actions/collaborator.actions.ts`)
- `findAllCollaboratorAction()` - Busca todos os colaboradores

#### Células (`src/modules/cell/actions/cell.actions.ts`)
- `findAllCellAction()` - Busca todas as células

#### Links de Formulários (`src/modules/inspection/actions/form-links.actions.ts`)
- `findAllFormLinksAction()` - Busca links de formulários

#### Campos (`src/modules/inspection/actions/fields.actions.ts`)
- `findAllFieldsAction()` - Busca todos os campos

#### Colaboradores por Setor (`src/modules/inspection/actions/collaborator-by-sector.actions.ts`)
- `findAllCollabBySectorAction()` - Busca colaboradores por setor

### 3. Modificação dos Hooks para Usar Server Actions

Todos os hooks foram modificados para usar as Server Actions ao invés de chamar `createGetRequest` diretamente:

**Antes:**
```typescript
const { data, isLoading, isFetched } = useQuery({
  queryKey: inspectionKeys.forms.list({ page, limit, search }),
  queryFn: () => createGetRequest<IResponsePaginated<IFormDto>>(
    INSPECTION_FORM_ENDPOINTS.FIND_ALL({ page, limit, search })
  ),
  enabled: canRead(INSPECTION_SUBJECTS.INSPECTION_FORM),
});
```

**Depois:**
```typescript
const { data, isLoading, isFetched } = useQuery({
  queryKey: inspectionKeys.forms.list({ page, limit, search }),
  queryFn: () => findAllFormsAction({ page, limit, search }),
  enabled: canRead(INSPECTION_SUBJECTS.INSPECTION_FORM),
});
```

### 4. Tratamento de Erros Aprimorado

Modificamos `handleGlobalError` em `src/shared/lib/errors/handler.ts` para garantir serialização:

```typescript
export function handleGlobalError(error: unknown): IHandleResponseError {
  const errorDetails = errorExtractor.extractErrorDetails(error);

  if (error instanceof AxiosError) {
    const status = error.response?.status ?? 0;
    // Garantir que apenas dados serializáveis sejam retornados
    const serializableDetails = error.response?.data ? 
      JSON.parse(JSON.stringify(error.response.data)) : undefined;
    
    return {
      success: false,
      data: {
        message: errorDetails.message,
        method: error.config?.method,
        url: error.config?.url,
        details: serializableDetails,
        category: errorDetails.category,
        fields: errorDetails.fields,
        code: errorDetails.code,
        context: errorDetails.context ? 
          JSON.parse(JSON.stringify(errorDetails.context)) : undefined,
      },
      status: status,
    };
  }
  // ... resto da implementação
}
```

## Benefícios da Implementação

1. **Execução Garantida no Servidor**: Todas as requisições HTTP são executadas no servidor através de Server Actions
2. **Dados Serializáveis**: Eliminação completa de objetos Axios não serializáveis
3. **Melhor Performance**: Redução de hidratação desnecessária no cliente
4. **Segurança**: Credenciais e configurações sensíveis permanecem no servidor
5. **Compatibilidade com Next.js**: Alinhamento com as melhores práticas do Next.js 15
6. **Manutenibilidade**: Separação clara entre lógica de servidor e cliente

## Arquivos Modificados

### Novos Arquivos (Server Actions)
- `src/modules/inspection/actions/form.actions.ts`
- `src/modules/inspection/actions/measures.actions.ts`
- `src/modules/inspection/actions/cell-components.actions.ts`
- `src/modules/collaborator/actions/collaborator.actions.ts`
- `src/modules/cell/actions/cell.actions.ts`
- `src/modules/inspection/actions/form-links.actions.ts`
- `src/modules/inspection/actions/fields.actions.ts`
- `src/modules/inspection/actions/collaborator-by-sector.actions.ts`

### Arquivos Modificados
- `src/shared/lib/requests/helpers/request-helpers.ts`
- `src/shared/lib/errors/handler.ts`
- Todos os hooks de listagem que usavam `createGetRequest` diretamente

## Resultado

O erro `AxiosHeaders objects are not supported` foi completamente eliminado, garantindo que todas as requisições sejam executadas no servidor e apenas dados serializáveis sejam passados para os Client Components.
